# ================================
# Dockerfile para React Frontend
# Ubicación: cerebro-fe/Dockerfile
# ================================

# Stage base - Node.js
FROM node:18-alpine AS base
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Stage de desarrollo
FROM node:18-alpine AS development
WORKDIR /app

# Instalar dependencias
COPY package*.json ./
RUN npm install

# Copiar código fuente
COPY . .

# Variables de entorno para desarrollo
ENV NODE_ENV=development
ENV CHOKIDAR_USEPOLLING=true
ENV GENERATE_SOURCEMAP=true
ENV FAST_REFRESH=true

# Exponer puerto
EXPOSE 3000

# Comando de desarrollo con hot reload
CMD ["npm", "start"]

# Stage de construcción
FROM node:18-alpine AS build
WORKDIR /app

# Copiar dependencias
COPY --from=base /app/node_modules ./node_modules
COPY package*.json ./

# Copiar código fuente y construir
COPY . .
RUN npm run build

# Stage de producción
FROM nginx:alpine AS production
WORKDIR /usr/share/nginx/html

# Remover archivos por defecto de nginx
RUN rm -rf ./*

# Copiar build de React
COPY --from=build /app/build .

# Configuración de nginx para SPA
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Exponer puerto
EXPOSE 80

# Comando para producción
CMD ["nginx", "-g", "daemon off;"]

# Stage por defecto (desarrollo)
FROM development
