# auth_enabled: false

# server:
#   http_listen_port: 3100
#   grpc_listen_port: 9095
#   log_level: info

# common:
#   instance_addr: 127.0.0.1
#   path_prefix: /loki
#   storage:
#     filesystem:
#       chunks_directory: /loki/chunks
#       rules_directory: /loki/rules
#   replication_factor: 1
#   ring:
#     kvstore:
#       store: inmemory

# query_range:
#   results_cache:
#     cache:
#       embedded_cache:
#         enabled: true
#         max_size_mb: 100

# schema_config:
#   configs:
#     - from: 2020-10-24
#       store: tsdb
#       object_store: filesystem
#       schema: v13
#       index:
#         prefix: index_
#         period: 24h

# ruler:
#   alertmanager_url: http://localhost:9093

# # Habilitar la interfaz web experimental
# frontend:
#   log_queries_longer_than: 5s
#   compress_responses: true

# # Configuración de límites
# limits_config:
#   reject_old_samples: true
#   reject_old_samples_max_age: 168h
#   max_cache_freshness_per_query: 10m
#   split_queries_by_interval: 15m

# # Analytics deshabilitado
# analytics:
#   reporting_enabled: false

auth_enabled: false

server:
  http_listen_port: 3100

common:
  path_prefix: /loki
  instance_addr: 127.0.0.1
  storage:
    filesystem:
      chunks_directory: /loki/chunks
      rules_directory: /loki/rules
  replication_factor: 1
  ring:
    kvstore:
      store: inmemory

query_range:
  results_cache:
    cache:
      embedded_cache:
        enabled: true
        max_size_mb: 100

schema_config:
  configs:
    - from: 2020-05-15
      store: tsdb
      object_store: filesystem
      schema: v13
      index:
        prefix: index_
        period: 24h

ruler:
  alertmanager_url: http://localhost:9093

analytics:
  reporting_enabled: false

limits_config:
  retention_period: 30d

compactor:
  working_directory: /loki/retention
  delete_request_store: filesystem
  retention_enabled: true
  retention_delete_delay: 2h
