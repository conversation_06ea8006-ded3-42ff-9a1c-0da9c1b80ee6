# docker.mk - Gestión específica de Docker para MSarkNet
# Uso: make -f docker.mk [comando]

# Variables Docker
DOCKER_COMPOSE := docker compose
PROJECT := msarknet
NETWORK := proxy
SERVICES := traefik main-app grafana prometheus whoami portainer docs

# Colores
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
PURPLE := \033[0;35m
CYAN := \033[0;36m
NC := \033[0m

.DEFAULT_GOAL := help

##@ 🐳 Docker Compose Operations

.PHONY: dc-up
dc-up: ## 🚀 Docker compose up (detached)
	@echo -e "$(GREEN)✅ Volumes backed up to backups/ directory$(NC)"

.PHONY: restore-volumes
restore-volumes: ## 🔄 Restore Docker volumes from backup
	@echo -e "$(BLUE)🔄 Available backups:$(NC)"
	@ls -la backups/ 2>/dev/null || echo "No backups found"
	@echo -e "$(YELLOW)To restore: docker run --rm -v $(PROJECT)_letsencrypt:/data -v $(PWD)/backups:/backup busybox tar xzf /backup/[backup-file] -C /data$(NC)"

##@ 🧪 Testing and Development

.PHONY: dev-mode
dev-mode: ## 🚀 Start in development mode with live logs
	@$(DOCKER_COMPOSE) up -d
	@echo -e "$(GREEN)🚀 Development mode started$(NC)"
	@$(DOCKER_COMPOSE) logs -f traefik

.PHONY: test-connectivity
test-connectivity: ## 🧪 Test container connectivity
	@echo -e "$(CYAN)🧪 Testing container connectivity...$(NC)"
	@for service in $(SERVICES); do \
		container_id=$($(DOCKER_COMPOSE) ps -q $service 2>/dev/null); \
		if [ ! -z "$container_id" ]; then \
			echo -e "$(BLUE)Testing $service...$(NC)"; \
			docker exec $container_id ping -c 1 traefik >/dev/null 2>&1 && echo -e "  ✅ Can reach Traefik" || echo -e "  ❌ Cannot reach Traefik"; \
		fi; \
	done

.PHONY: test-endpoints
test-endpoints: ## 🧪 Test all HTTP endpoints
	@echo -e "$(CYAN)🧪 Testing HTTP endpoints...$(NC)"
	@echo -e "$(BLUE)Main app:$(NC)"
	@curl -k -s -o /dev/null -w "  Status: %{http_code} - Time: %{time_total}s\n" https://localhost/ || echo "  ❌ Failed"
	@echo -e "$(BLUE)API:$(NC)"
	@curl -k -s -o /dev/null -w "  Status: %{http_code} - Time: %{time_total}s\n" https://localhost/api || echo "  ❌ Failed"
	@echo -e "$(BLUE)Traefik Dashboard:$(NC)"
	@curl -s -o /dev/null -w "  Status: %{http_code} - Time: %{time_total}s\n" http://localhost:8080/api/rawdata || echo "  ❌ Failed"

.PHONY: benchmark-api
benchmark-api: ## 📊 Benchmark API performance
	@echo -e "$(CYAN)📊 Benchmarking API...$(NC)"
	@if command -v ab >/dev/null 2>&1; then \
		ab -n 1000 -c 10 -k -H "Host: msarknet.me" https://localhost/api 2>/dev/null | grep -E "Requests per second|Time per request|Failed requests"; \
	else \
		echo -e "$(YELLOW)⚠️  Apache Bench (ab) not installed$(NC)"; \
	fi

##@ 🔐 Security Operations

.PHONY: security-scan
security-scan: ## 🔐 Basic security scan of containers
	@echo -e "$(CYAN)🔐 Security scan...$(NC)"
	@for service in $(SERVICES); do \
		container_id=$($(DOCKER_COMPOSE) ps -q $service 2>/dev/null); \
		if [ ! -z "$container_id" ]; then \
			echo -e "$(BLUE)Scanning $service...$(NC)"; \
			docker exec $container_id ps aux | grep -E "(root|www-data)" | wc -l | xargs echo "  Processes:"; \
			docker exec $container_id netstat -tlnp 2>/dev/null | grep LISTEN | wc -l | xargs echo "  Open ports:"; \
		fi; \
	done

.PHONY: check-permissions
check-permissions: ## 🔐 Check file permissions
	@echo -e "$(CYAN)🔐 Checking file permissions...$(NC)"
	@ls -la certs/ dynamic/ web-content/ 2>/dev/null || echo "Some directories not found"

##@ 📈 Performance Operations

.PHONY: optimize
optimize: ## ⚡ Optimize Docker performance
	@echo -e "$(YELLOW)⚡ Optimizing Docker...$(NC)"
	@docker system prune -f
	@$(DOCKER_COMPOSE) up -d --remove-orphans

.PHONY: resource-limits
resource-limits: ## 📊 Show container resource limits
	@echo -e "$(CYAN)📊 Container resource limits:$(NC)"
	@for service in $(SERVICES); do \
		container_id=$($(DOCKER_COMPOSE) ps -q $service 2>/dev/null); \
		if [ ! -z "$container_id" ]; then \
			echo -e "$(BLUE)$service:$(NC)"; \
			docker inspect $container_id | jq -r '.[0].HostConfig | "  CPU: \(.CpuShares // "unlimited"), Memory: \(.Memory // "unlimited")"' 2>/dev/null || echo "  No limits set"; \
		fi; \
	done

##@ 💾 Backup and Restore

.PHONY: backup-config
backup-config: ## 💾 Backup configuration files
	@echo -e "$(BLUE)💾 Backing up configuration...$(NC)"
	@mkdir -p backups/config
	@cp -r dynamic/ backups/config/ 2>/dev/null || true
	@cp docker-compose.yml backups/config/ 2>/dev/null || true
	@cp Makefile docker.mk backups/config/ 2>/dev/null || true
	@tar czf backups/config-backup-$(shell date +%Y%m%d-%H%M%S).tar.gz -C backups/config .
	@echo -e "$(GREEN)✅ Configuration backed up$(NC)"

.PHONY: export-compose
export-compose: ## 📤 Export compose configuration
	@echo -e "$(BLUE)📤 Exporting compose config...$(NC)"
	@$(DOCKER_COMPOSE) config > docker-compose-exported.yml
	@echo -e "$(GREEN)✅ Config exported to docker-compose-exported.yml$(NC)"

##@ 🔄 Update Operations

.PHONY: update-traefik
update-traefik: ## 🔄 Update Traefik to latest version
	@echo -e "$(BLUE)🔄 Updating Traefik...$(NC)"
	@docker pull traefik:v3.4
	@$(DOCKER_COMPOSE) up -d --no-deps traefik

.PHONY: update-nginx
update-nginx: ## 🔄 Update Nginx containers
	@echo -e "$(BLUE)🔄 Updating Nginx...$(NC)"
	@docker pull nginx:alpine
	@$(DOCKER_COMPOSE) up -d --no-deps main-app portainer-mock docs

.PHONY: update-all-images
update-all-images: ## 🔄 Update all container images
	@echo -e "$(BLUE)🔄 Updating all images...$(NC)"
	@docker pull traefik:v3.4
	@docker pull nginx:alpine
	@docker pull traefik/whoami
	@$(DOCKER_COMPOSE) up -d --force-recreate

##@ 🎯 Quick Actions

.PHONY: quick-restart
quick-restart: dc-down dc-up ## ⚡ Quick restart all services

.PHONY: quick-logs
quick-logs: ## ⚡ Quick view of recent logs
	@$(DOCKER_COMPOSE) logs --tail=50

.PHONY: quick-status
quick-status: ## ⚡ Quick status check
	@echo -e "$(CYAN)⚡ Quick Status:$(NC)"
	@$(DOCKER_COMPOSE) ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"

.PHONY: quick-test
quick-test: ## ⚡ Quick functionality test
	@curl -k -s https://localhost/ | grep -q "MSarkNet" && echo -e "$(GREEN)✅ Quick test passed$(NC)" || echo -e "$(RED)❌ Quick test failed$(NC)"

##@ 📚 Information

.PHONY: docker-info
docker-info: ## ℹ️  Show Docker system information
	@echo -e "$(CYAN)ℹ️  Docker System Information:$(NC)"
	@echo -e "$(BLUE)Docker version:$(NC)"
	@docker --version
	@echo -e "$(BLUE)Docker Compose version:$(NC)"
	@docker compose version
	@echo -e "$(BLUE)Available networks:$(NC)"
	@docker network ls | grep -E "($(NETWORK)|bridge)"
	@echo -e "$(BLUE)Project containers:$(NC)"
	@$(DOCKER_COMPOSE) ps

.PHONY: container-sizes
container-sizes: ## 📊 Show container sizes
	@echo -e "$(CYAN)📊 Container sizes:$(NC)"
	@docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep -E "(traefik|nginx|whoami)"

.PHONY: help
help: ## 💡 Show this help
	@awk 'BEGIN {FS = ":.*##"; printf "\n$(CYAN)🐳 MSarkNet Docker Management$(NC)\n\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  $(GREEN)%-25s$(NC) %s\n", $1, $2 } /^##@/ { printf "\n$(YELLOW)%s$(NC)\n", substr($0, 5) } ' $(MAKEFILE_LIST)

##@ 🎮 Interactive Commands

.PHONY: interactive-logs
interactive-logs: ## 🎮 Interactive log viewer
	@echo -e "$(CYAN)🎮 Interactive log viewer$(NC)"
	@echo -e "$(BLUE)Available services:$(NC) $(SERVICES)"
	@read -p "Enter service name (or 'all' for all logs): " service; \
	if [ "$service" = "all" ]; then \
		$(DOCKER_COMPOSE) logs -f; \
	else \
		$(DOCKER_COMPOSE) logs -f $service; \
	fi

.PHONY: interactive-shell
interactive-shell: ## 🎮 Interactive shell access
	@echo -e "$(CYAN)🎮 Interactive shell access$(NC)"
	@echo -e "$(BLUE)Available services:$(NC) $(SERVICES)"
	@read -p "Enter service name: " service; \
	$(DOCKER_COMPOSE) exec $service sh || $(DOCKER_COMPOSE) exec $service bash

.PHONY: interactive-scale
interactive-scale: ## 🎮 Interactive service scaling
	@echo -e "$(CYAN)🎮 Interactive service scaling$(NC)"
	@echo -e "$(BLUE)Scalable services:$(NC) api-service main-app"
	@read -p "Enter service name: " service; \
	read -p "Enter number of instances: " instances; \
	$(DOCKER_COMPOSE) up -d --scale $service=$instances

##@ 🔧 Maintenance

.PHONY: maintenance-start
maintenance-start: ## 🔧 Start maintenance mode
	@echo -e "$(YELLOW)🔧 Starting maintenance mode...$(NC)"
	@$(DOCKER_COMPOSE) scale main-app=0
	@echo -e "$(YELLOW)⚠️  Main app scaled to 0. Users will see 503 errors$(NC)"

.PHONY: maintenance-end
maintenance-end: ## 🔧 End maintenance mode
	@echo -e "$(GREEN)🔧 Ending maintenance mode...$(NC)"
	@$(DOCKER_COMPOSE) scale main-app=1
	@echo -e "$(GREEN)✅ Main app restored$(NC)"

# Include main Makefile if exists
-include Makefile🚀 Starting containers...$(NC)"
	@$(DOCKER_COMPOSE) up -d

.PHONY: dc-up-build
dc-up-build: ## 🔨 Docker compose up with build
	@echo -e "$(YELLOW)🔨 Building and starting containers...$(NC)"
	@$(DOCKER_COMPOSE) up -d --build

.PHONY: dc-down
dc-down: ## ⏹️  Docker compose down
	@echo -e "$(RED)⏹️  Stopping containers...$(NC)"
	@$(DOCKER_COMPOSE) down

.PHONY: dc-down-v
dc-down-v: ## ⏹️  Docker compose down with volumes
	@echo -e "$(RED)⏹️  Stopping containers and removing volumes...$(NC)"
	@$(DOCKER_COMPOSE) down -v

.PHONY: dc-restart
dc-restart: ## 🔄 Docker compose restart
	@echo -e "$(BLUE)🔄 Restarting containers...$(NC)"
	@$(DOCKER_COMPOSE) restart

.PHONY: dc-pull
dc-pull: ## ⬇️  Pull latest images
	@echo -e "$(CYAN)⬇️  Pulling latest images...$(NC)"
	@$(DOCKER_COMPOSE) pull

.PHONY: dc-ps
dc-ps: ## 📋 Show container status
	@$(DOCKER_COMPOSE) ps

.PHONY: dc-logs
dc-logs: ## 📝 Show all logs
	@$(DOCKER_COMPOSE) logs -f

.PHONY: dc-config
dc-config: ## 🔧 Validate and view compose config
	@$(DOCKER_COMPOSE) config

##@ 🎯 Service Management

.PHONY: start-traefik
start-traefik: ## ▶️  Start only Traefik
	@$(DOCKER_COMPOSE) up -d traefik

.PHONY: stop-traefik
stop-traefik: ## ⏹️  Stop only Traefik
	@$(DOCKER_COMPOSE) stop traefik

.PHONY: restart-traefik
restart-traefik: ## 🔄 Restart only Traefik
	@$(DOCKER_COMPOSE) restart traefik

.PHONY: start-app
start-app: ## ▶️  Start main application
	@$(DOCKER_COMPOSE) up -d main-app

.PHONY: stop-app
stop-app: ## ⏹️  Stop main application
	@$(DOCKER_COMPOSE) stop main-app

.PHONY: restart-app
restart-app: ## 🔄 Restart main application
	@$(DOCKER_COMPOSE) restart main-app

.PHONY: start-api
start-api: ## ▶️  Start API service
	@$(DOCKER_COMPOSE) up -d api-service

.PHONY: stop-api
stop-api: ## ⏹️  Stop API service
	@$(DOCKER_COMPOSE) stop api-service

.PHONY: restart-api
restart-api: ## 🔄 Restart API service
	@$(DOCKER_COMPOSE) restart api-service

##@ 📊 Scaling Operations

.PHONY: scale-api-3
scale-api-3: ## ⚖️  Scale API service to 3 instances
	@echo -e "$(BLUE)⚖️  Scaling API to 3 instances...$(NC)"
	@$(DOCKER_COMPOSE) up -d --scale api-service=3

.PHONY: scale-api-5
scale-api-5: ## ⚖️  Scale API service to 5 instances
	@echo -e "$(BLUE)⚖️  Scaling API to 5 instances...$(NC)"
	@$(DOCKER_COMPOSE) up -d --scale api-service=5

.PHONY: scale-api-1
scale-api-1: ## ⚖️  Scale API service back to 1 instance
	@echo -e "$(BLUE)⚖️  Scaling API back to 1 instance...$(NC)"
	@$(DOCKER_COMPOSE) up -d --scale api-service=1

.PHONY: scale-app-3
scale-app-3: ## ⚖️  Scale main app to 3 instances
	@echo -e "$(BLUE)⚖️  Scaling main app to 3 instances...$(NC)"
	@$(DOCKER_COMPOSE) up -d --scale main-app=3

.PHONY: scale-down
scale-down: ## ⚖️  Scale all services back to 1 instance
	@echo -e "$(BLUE)⚖️  Scaling all services to 1 instance...$(NC)"
	@$(DOCKER_COMPOSE) up -d --scale api-service=1 --scale main-app=1

##@ 📝 Logs Management

.PHONY: logs-traefik
logs-traefik: ## 📝 Show Traefik logs
	@$(DOCKER_COMPOSE) logs -f traefik

.PHONY: logs-app
logs-app: ## 📝 Show main app logs
	@$(DOCKER_COMPOSE) logs -f main-app

.PHONY: logs-api
logs-api: ## 📝 Show API logs
	@$(DOCKER_COMPOSE) logs -f api-service

.PHONY: logs-grafana
logs-grafana: ## 📝 Show Grafana logs
	@$(DOCKER_COMPOSE) logs -f grafana-mock

.PHONY: logs-prometheus
logs-prometheus: ## 📝 Show Prometheus logs
	@$(DOCKER_COMPOSE) logs -f prometheus-mock

.PHONY: logs-portainer
logs-portainer: ## 📝 Show Portainer logs
	@$(DOCKER_COMPOSE) logs -f portainer-mock

.PHONY: logs-docs
logs-docs: ## 📝 Show Docs logs
	@$(DOCKER_COMPOSE) logs -f docs

.PHONY: logs-tail
logs-tail: ## 📝 Tail last 100 lines of all logs
	@$(DOCKER_COMPOSE) logs --tail=100

##@ 🐚 Shell Access

.PHONY: shell-traefik
shell-traefik: ## 🐚 Access Traefik shell
	@$(DOCKER_COMPOSE) exec traefik sh

.PHONY: shell-app
shell-app: ## 🐚 Access main app shell
	@$(DOCKER_COMPOSE) exec main-app sh

.PHONY: shell-api
shell-api: ## 🐚 Access API shell
	@$(DOCKER_COMPOSE) exec api-service sh

##@ 🔍 Inspection and Debugging

.PHONY: inspect-traefik
inspect-traefik: ## 🔍 Inspect Traefik container
	@docker inspect $(shell $(DOCKER_COMPOSE) ps -q traefik)

.PHONY: inspect-network
inspect-network: ## 🔍 Inspect proxy network
	@docker network inspect $(NETWORK)

.PHONY: inspect-all
inspect-all: ## 🔍 Inspect all containers
	@for service in $(SERVICES); do \
		echo -e "$(CYAN)🔍 Inspecting $$service...$(NC)"; \
		$(DOCKER_COMPOSE) ps $$service 2>/dev/null || echo "Service $$service not found"; \
	done

.PHONY: top
top: ## 📊 Show running processes in containers
	@echo -e "$(CYAN)📊 Running processes:$(NC)"
	@$(DOCKER_COMPOSE) top

.PHONY: port-traefik
port-traefik: ## 🔌 Show Traefik port mapping
	@$(DOCKER_COMPOSE) port traefik

.PHONY: port-all
port-all: ## 🔌 Show all port mappings
	@for service in $(SERVICES); do \
		echo -e "$(BLUE)Ports for $$service:$(NC)"; \
		$(DOCKER_COMPOSE) port $$service 2>/dev/null || echo "No exposed ports"; \
		echo ""; \
	done

##@ 🧹 Docker Cleanup

.PHONY: docker-clean
docker-clean: ## 🧹 Remove unused Docker resources
	@echo -e "$(YELLOW)🧹 Cleaning Docker resources...$(NC)"
	@docker system prune -f

.PHONY: docker-clean-all
docker-clean-all: ## 🧹 Remove all unused Docker resources (including volumes)
	@echo -e "$(RED)🧹 Cleaning all Docker resources...$(NC)"
	@docker system prune -a -f --volumes

.PHONY: docker-clean-images
docker-clean-images: ## 🧹 Remove unused Docker images
	@echo -e "$(YELLOW)🧹 Cleaning Docker images...$(NC)"
	@docker image prune -a -f

.PHONY: docker-clean-volumes
docker-clean-volumes: ## 🧹 Remove unused Docker volumes
	@echo -e "$(YELLOW)🧹 Cleaning Docker volumes...$(NC)"
	@docker volume prune -f

.PHONY: docker-clean-networks
docker-clean-networks: ## 🧹 Remove unused Docker networks
	@echo -e "$(YELLOW)🧹 Cleaning Docker networks...$(NC)"
	@docker network prune -f

##@ 🌐 Network Management

.PHONY: network-create
network-create: ## 🌐 Create proxy network
	@echo -e "$(BLUE)🌐 Creating $(NETWORK) network...$(NC)"
	@docker network create $(NETWORK) 2>/dev/null || echo -e "$(YELLOW)⚠️  Network already exists$(NC)"

.PHONY: network-remove
network-remove: ## 🌐 Remove proxy network
	@echo -e "$(RED)🌐 Removing $(NETWORK) network...$(NC)"
	@docker network rm $(NETWORK) 2>/dev/null || echo -e "$(YELLOW)⚠️  Network doesn't exist$(NC)"

.PHONY: network-ls
network-ls: ## 🌐 List Docker networks
	@docker network ls

.PHONY: network-connect-traefik
network-connect-traefik: ## 🌐 Connect Traefik to proxy network
	@docker network connect $(NETWORK) $(shell $(DOCKER_COMPOSE) ps -q traefik) 2>/dev/null || echo -e "$(YELLOW)⚠️  Already connected$(NC)"

##@ 📊 Statistics and Monitoring

.PHONY: stats
stats: ## 📊 Show container resource usage
	@docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"

.PHONY: stats-live
stats-live: ## 📊 Show live container resource usage
	@docker stats

.PHONY: health
health: ## 🏥 Check container health
	@echo -e "$(CYAN)🏥 Container Health Status:$(NC)"
	@for service in $(SERVICES); do \
		container_id=$$($(DOCKER_COMPOSE) ps -q $$service 2>/dev/null); \
		if [ ! -z "$$container_id" ]; then \
			health=$$(docker inspect --format='{{.State.Health.Status}}' $$container_id 2>/dev/null || echo "no-healthcheck"); \
			echo -e "  $(BLUE)$$service:$(NC) $$health"; \
		else \
			echo -e "  $(BLUE)$$service:$(NC) $(RED)not running$(NC)"; \
		fi; \
	done

##@ 🔧 Advanced Operations

.PHONY: recreate-traefik
recreate-traefik: ## 🔄 Recreate Traefik container
	@echo -e "$(YELLOW)🔄 Recreating Traefik...$(NC)"
	@$(DOCKER_COMPOSE) up -d --force-recreate traefik

.PHONY: recreate-all
recreate-all: ## 🔄 Recreate all containers
	@echo -e "$(YELLOW)🔄 Recreating all containers...$(NC)"
	@$(DOCKER_COMPOSE) up -d --force-recreate

.PHONY: update-images
update-images: dc-pull dc-up-build ## 🔄 Update all images and rebuild

.PHONY: backup-volumes
backup-volumes: ## 💾 Backup Docker volumes
	@echo -e "$(BLUE)💾 Backing up volumes...$(NC)"
	@mkdir -p backups
	@docker run --rm -v $(PROJECT)_letsencrypt:/data -v $(PWD)/backups:/backup busybox tar czf /backup/letsencrypt-$(shell date +%Y%m%d-%H%M%S).tar.gz -C /data .
	@echo -e "$(GREEN)
