tls:
  certificates:
    - certFile: /certs/msarknet.me.crt
      keyFile: /certs/msarknet.me.key
  options:
    default:
      minVersion: VersionTLS12

# tls:
#   certificates:
#     - certFile: /certs/msarknet.me.crt
#       keyFile: /certs/msarknet.me.key
#   options:
#     default:
#       minVersion: "VersionTLS12"
#       maxVersion: "VersionTLS13"
#       cipherSuites:
#         - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
#         - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
#         - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
#       curvePreferences:
#         - CurveP521
#         - CurveP384
