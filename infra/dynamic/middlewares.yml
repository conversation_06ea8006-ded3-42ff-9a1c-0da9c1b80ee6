http:
  middlewares:
    secure-headers:
      headers:
        stsSeconds: 31536000
        stsIncludeSubdomains: true
        stsPreload: true
        forceSTSHeader: true
        frameDeny: true
        contentTypeNosniff: true
        browserXssFilter: true

    compression:
      compress: {}

    cors-headers:
      headers:
        accessControlAllowOriginList:
          - "https://msarknet.me"
          - "https://*.msarknet.me"
        accessControlAllowMethods:
          - GET
          - POST
          - OPTIONS
        accessControlAllowHeaders:
          - "*"
        addVaryHeader: true

    rate-limit:
      rateLimit:
        average: 100
        burst: 50

    # Basic auth para dashboard/prometheus
    auth:
      basicAuth:
        users:
          # htpasswd -nbB admin "admin" - usuario: admin, password: admin
          - "admin:$2y$05$A6SpBprijrp3maYZnduBAOphf7hsi0CnCySZNKJKBgSVwO8Klg2gq"

# http:
#   middlewares:
#     # Headers de seguridad
#     secure-headers:
#       headers:
#         frameDeny: true
#         sslRedirect: true
#         browserXssFilter: true
#         contentTypeNosniff: true
#         forceSTSHeader: true
#         stsIncludeSubdomains: true
#         stsPreload: true
#         stsSeconds: 31536000
#         contentSecurityPolicy: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
#         referrerPolicy: "strict-origin-when-cross-origin"

#     # Headers específicos para SPA (React)
#     spa-headers:
#       headers:
#         frameDeny: true
#         sslRedirect: true
#         browserXssFilter: true
#         contentTypeNosniff: true
#         forceSTSHeader: true
#         stsIncludeSubdomains: true
#         stsPreload: true
#         stsSeconds: 31536000
#         # CSP más permisivo para React
#         contentSecurityPolicy: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' data:; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.msarknet.me wss://api.msarknet.me;"
#         referrerPolicy: "strict-origin-when-cross-origin"
#         # Headers para evitar cache de HTML en SPA
#         customResponseHeaders:
#           Cache-Control: "no-cache, no-store, must-revalidate"
#           Pragma: "no-cache"
#           Expires: "0"

#     # CORS para APIs
#     cors-headers:
#       headers:
#         accessControlAllowMethods:
#           - GET
#           - POST
#           - PUT
#           - DELETE
#           - OPTIONS
#           - PATCH
#         accessControlAllowHeaders:
#           - "Content-Type"
#           - "Authorization"
#           - "X-Requested-With"
#           - "Accept"
#           - "Origin"
#           - "Access-Control-Request-Method"
#           - "Access-Control-Request-Headers"
#         accessControlAllowOriginList:
#           - "https://app.msarknet.me"
#           - "https://msarknet.me"
#           - "https://*.msarknet.me"
#         accessControlMaxAge: 86400
#         accessControlAllowCredentials: true

#     # CORS preflight para APIs
#     cors-preflight:
#       headers:
#         accessControlAllowMethods:
#           - GET
#           - POST
#           - PUT
#           - DELETE
#           - OPTIONS
#           - PATCH
#         accessControlAllowHeaders:
#           - "*"
#         accessControlAllowOriginList:
#           - "https://app.msarknet.me"
#         accessControlMaxAge: 86400
#         accessControlAllowCredentials: true

#     # Autenticación básica para servicios sensibles
#     auth:
#       basicAuth:
#         users:
#           # admin:admin123 (cambiar en producción)
#           - "admin:$2a$10$1H4N8B8B8B8B8B8B8B8B8OuKUjGVjEhF0ZhE8WNLoxF7y7QtfJrRy"

#     # Lista de IPs permitidas (ajusta según tu red local)
#     ip-allowlist:
#       ipAllowList:
#         sourceRange:
#           - "127.0.0.1/32"
#           - "***********/16"
#           - "10.0.0.0/8"
#           - "**********/12"

#     # Rate limiting más estricto para APIs
#     rate-limit:
#       rateLimit:
#         average: 100
#         period: "1m"
#         burst: 50

#     # Rate limiting más permisivo para frontend
#     rate-limit-spa:
#       rateLimit:
#         average: 200
#         period: "1m"
#         burst: 100

#     # Compression
#     compression:
#       compress: {}

#     # Middleware para desarrollo - Hot reload websocket
#     websocket-headers:
#       headers:
#         customRequestHeaders:
#           Connection: "upgrade"
#           Upgrade: "$http_upgrade"
#         customResponseHeaders:
#           Access-Control-Allow-Origin: "*"
