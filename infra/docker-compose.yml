networks:
  proxy:
    external: true
    name: proxy
    driver: bridge

volumes:
  grafana_data:
  loki-data:
  prometheus_data:
  portainer_data:
  mariadb-data:

services:
  traefik:
    container_name: "traefik"
    image: "traefik:v3.5.2"
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    command:
      - "--api.dashboard=true" # ⚠️ Solo en local
      - "--api.insecure=true" # ⚠️ Solo en local
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--providers.docker.network=proxy"
      - "--providers.file.directory=/etc/traefik/dynamic"
      - "--providers.file.watch=true"
      - "--entryPoints.web.address=:80"
      - "--entryPoints.websecure.address=:443"
      - "--entryPoints.websecure.http.tls=true"
      # redirección http -> https
      - "--entryPoints.web.http.redirections.entryPoint.to=websecure"
      - "--entryPoints.web.http.redirections.entryPoint.scheme=https"
      # Let's Encrypt (TLS challenge)
      # - "--certificatesresolvers.le.acme.httpchallenge=true"
      # - "--certificatesresolvers.le.acme.tlschallenge=true"
      # - "--certificatesresolvers.le.acme.httpchallenge.entrypoint=web"
      # - "--certificatesresolvers.le.acme.email=${LETSENCRYPT_EMAIL}"
      # - "--certificatesresolvers.le.acme.storage=/letsencrypt/acme.json"
      # logs
      - "--accesslog=true"
      - "--accesslog.filepath=/var/log/traefik/access.log"
      - "--log.level=INFO"
      # Habilitar métricas para Prometheus
      - "--metrics.prometheus=true"
      - "--entryPoints.metrics.address=:8082"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
      - "8082:8082"  # Puerto para métricas
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "./certs:/certs:ro"
      - "./dynamic:/etc/traefik/dynamic:ro"
      # Para Let's Encrypt en producción
      # - "./letsencrypt:/letsencrypt"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`traefik.msarknet.me`)"
      - "traefik.http.routers.dashboard.entrypoints=websecure"
      - "traefik.http.routers.dashboard.service=api@internal"
      - "traefik.http.routers.dashboard.tls=true"
      - "traefik.http.routers.dashboard.middlewares=secure-headers@file,auth@file"
      # - traefik.http.routers.traefik.tls.certresolver=le
    networks:
      - proxy

  whoami:
    container_name: "whoami"
    image: "traefik/whoami"
    restart: unless-stopped
    networks:
      - proxy
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.whoami.rule=Host(`whoami.msarknet.me`)"
      - "traefik.http.routers.whoami.entrypoints=websecure"
      - "traefik.http.routers.whoami.tls=true"
      - "traefik.http.routers.whoami.middlewares=secure-headers@file,cors-headers@file,rate-limit@file"

  grafana:
    container_name: "grafana"
    image: "grafana/grafana:latest"
    restart: unless-stopped
    networks:
      - proxy
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
      - GF_SECURITY_ADMIN_EMAIL=${GRAFANA_ADMIN_EMAIL:-<EMAIL>}
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.msarknet.me`)"
      - "traefik.http.routers.grafana.entrypoints=websecure"
      - "traefik.http.routers.grafana.tls=true"
      - "traefik.http.routers.grafana.middlewares=secure-headers@file"
      - "traefik.http.services.grafana.loadbalancer.server.port=3000"

  loki: # 404 page not found
    container_name: "loki"
    image: "grafana/loki:3.5"
    restart: unless-stopped
    networks:
      - proxy
    volumes:
      - "./loki-config.yaml:/etc/loki/local-config.yaml:ro"
      - loki-data:/loki
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.loki.rule=Host(`loki.msarknet.me`)"
      - "traefik.http.routers.loki.entrypoints=websecure"
      - "traefik.http.routers.loki.tls=true"
      - "traefik.http.services.loki.loadbalancer.server.port=3100"

  # Keep Prometheus since we have the image locally
  prometheus:
    container_name: "prometheus"
    image: "prom/prometheus:latest"
    restart: unless-stopped
    networks:
      - proxy
    # ports:
    #   - "9090:9090"  # Direct port access since no Traefik
    volumes:
      - prometheus_data:/prometheus
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.prometheus.rule=Host(`prom.msarknet.me`)"
      - "traefik.http.routers.prometheus.entrypoints=websecure"
      - "traefik.http.routers.prometheus.tls=true"
      - "traefik.http.routers.prometheus.middlewares=secure-headers@file,auth@file"
      - "traefik.http.services.prometheus.loadbalancer.server.port=9090"

  # elasticsearch:

  # logstash:

  # kibana:

  # Keep Portainer since we have the image locally
  portainer:
    container_name: "portainer"
    image: "portainer/portainer-ce:latest"
    restart: unless-stopped
    networks:
      - proxy
    # ports:
    #   - "9000:9000"  # Direct port access since no Traefik
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - portainer_data:/data
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.portainer.rule=Host(`portainer.msarknet.me`)"
      - "traefik.http.routers.portainer.entrypoints=websecure"
      - "traefik.http.routers.portainer.tls=true"
      - "traefik.http.routers.portainer.middlewares=secure-headers@file"
      - "traefik.http.services.portainer.loadbalancer.server.port=9000"

  mariadb:
    container_name: mariadb
    image: mariadb:11
    environment:
      MARIADB_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MARIADB_DATABASE: ${DB_DATABASE}
      MARIADB_USER: ${DB_USER}
      MARIADB_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mariadb-data:/var/lib/mysql
    networks:
      - proxy
    restart: unless-stopped

  adminer:
    container_name: adminer
    image: adminer:latest
    depends_on:
      - mariadb
    environment:
      ADMINER_DEFAULT_SERVER: ${DB_HOST}
    networks:
      - proxy
    restart: unless-stopped
    labels:
      - traefik.enable=true
      - traefik.http.routers.dbadmin.rule=Host(`adminer.msarknet.me`)
      - traefik.http.routers.dbadmin.entrypoints=websecure
      - traefik.http.routers.dbadmin.tls=true
      - traefik.http.services.dbadmin.loadbalancer.server.port=8080

  # main-app:
  #   container_name: "main-app"
  #   image: "nginx:alpine"
  #   restart: unless-stopped
  #   networks:
  #     - proxy
  #   # ports:
  #   #   - "8080:80"  # Direct port access since no Traefik
  #   volumes:
  #     - "./web-content/main:/usr/share/nginx/html:ro"
  #   labels:
  #     - "traefik.enable=true"
  #     - "traefik.http.routers.main-app.rule=Host(`msarknet.me`)"
  #     # - "traefik.http.routers.api.rule=Host(`msarknet.me`) && PathPrefix(`/api`)"
  #     - "traefik.http.routers.main-app.entrypoints=websecure"
  #     - "traefik.http.routers.main-app.tls=true"
  #     - "traefik.http.routers.main-app.middlewares=secure-headers@file,compression@file"

  # ===============================
  # CEREBRO STACK - React Frontend
  # ===============================
  cerebro-fe:
    build:
      context: ../cerebro-fe
      dockerfile: Dockerfile
      target: development  # Usar stage de desarrollo
    container_name: "cerebro-fe"
    restart: unless-stopped
    networks:
      - proxy
    volumes:
      # Hot reload para desarrollo
      - "../cerebro-fe/src:/app/src"
      - "../cerebro-fe/public:/app/public"
      - "../cerebro-fe/package.json:/app/package.json"
      # Evitar conflictos con node_modules
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
      - REACT_APP_API_URL=https://api.msarknet.me
      - REACT_APP_WS_URL=wss://api.msarknet.me
      - GENERATE_SOURCEMAP=true
      - FAST_REFRESH=true
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cerebro-fe.rule=Host(`app.msarknet.me`)"
      - "traefik.http.routers.cerebro-fe.entrypoints=websecure"
      - "traefik.http.routers.cerebro-fe.tls=true"
      - "traefik.http.services.cerebro-fe.loadbalancer.server.port=3000"
      # Middleware para SPA (Single Page Application)
      - "traefik.http.routers.cerebro-fe.middlewares=spa-headers"

  docs:
    image: "nginx:alpine"
    container_name: "docs-service"
    restart: unless-stopped
    networks:
      - proxy
    # ports:
    #   - "8081:80"  # Direct port access since no Traefik
    volumes:
      - "./web-content/docs:/usr/share/nginx/html:ro"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.docs.rule=Host(`docs.msarknet.me`)"
      - "traefik.http.routers.docs.entrypoints=websecure"
      - "traefik.http.routers.docs.tls=true"
      - "traefik.http.routers.docs.middlewares=secure-headers@file,compression@file"
