# Makefile para gestión completa del entorno Traefik MSarkNet
# Uso: make [comando]

# Variables de configuración
COMPOSE_FILE := docker-compose.yml
PROJECT_NAME := msarknet
NETWORK_NAME := proxy
CERT_DOMAIN := msarknet.me

# Colores para output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
PURPLE := \033[0;35m
CYAN := \033[0;36m
NC := \033[0m # No Color

# Ayuda por defecto
.DEFAULT_GOAL := help

##@ 🚀 Gestión Principal

.PHONY: install
install: ## 📦 Instalación completa del entorno (primera vez)
	@echo -e "$(BLUE)🚀 Instalando entorno Traefik MSarkNet...$(NC)"
	@chmod +x _scripts/setup.sh _scripts/generate-certs.sh
	@./_scripts/setup.sh
	@echo -e "$(GREEN)✅ Instalación completada!$(NC)"

.PHONY: up
up: ## 🔄 Iniciar todos los servicios
	@echo -e "$(GREEN)🔄 Iniciando servicios...$(NC)"
	@docker network inspect $(NETWORK_NAME) >/dev/null 2>&1 || docker network create $(NETWORK_NAME)
	@docker compose -f $(COMPOSE_FILE) up -d
	@echo -e "$(GREEN)✅ Servicios iniciados$(NC)"
	@make status

.PHONY: down
down: ## ⏹️  Parar y eliminar todos los servicios
	@echo -e "$(RED)⏹️  Parando servicios...$(NC)"
	@docker compose -f $(COMPOSE_FILE) down
	@echo -e "$(RED)✅ Servicios parados$(NC)"

.PHONY: restart
restart: down up ## 🔄 Reiniciar todos los servicios

.PHONY: rebuild
rebuild: ## 🔨 Reconstruir y reiniciar servicios
	@echo -e "$(YELLOW)🔨 Reconstruyendo servicios...$(NC)"
	@docker compose -f $(COMPOSE_FILE) up -d --build --force-recreate
	@echo -e "$(GREEN)✅ Servicios reconstruidos$(NC)"

##@ 📊 Monitorización

.PHONY: status
status: ## 📊 Ver estado de todos los servicios
	@echo -e "$(CYAN)📊 Estado de los servicios:$(NC)"
	@docker compose -f $(COMPOSE_FILE) ps
	@echo ""
	@echo -e "$(CYAN)🌐 URLs disponibles:$(NC)"
	@echo -e "  $(BLUE)https://msarknet.me$(NC)              - App Principal"
	@echo -e "  $(BLUE)https://traefik.msarknet.me$(NC)     - Dashboard Traefik"
	@echo -e "  $(BLUE)https://grafana.msarknet.me$(NC)     - Grafana"
	@echo -e "  $(BLUE)https://prom.msarknet.me$(NC)        - Prometheus"
	@echo -e "  $(BLUE)https://portainer.msarknet.me$(NC)   - Portainer"
	@echo -e "  $(BLUE)https://docs.msarknet.me$(NC)        - Documentación"
	@echo -e "  $(BLUE)https://whoami.msarknet.me$(NC)      - API de testing (whoami)"

.PHONY: stats
stats: ## 📈 Ver estadísticas de recursos de contenedores
	@echo -e "$(CYAN)📈 Estadísticas de recursos:$(NC)"
	@docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

##@ 📝 Logs y Debugging

.PHONY: logs
logs: ## 📝 Ver logs de todos los servicios
	@docker compose -f $(COMPOSE_FILE) logs -f

.PHONY: logs-traefik
logs-traefik: ## 📝 Ver logs de Traefik únicamente
	@docker compose -f $(COMPOSE_FILE) logs -f traefik

.PHONY: logs-app
logs-app: ## 📝 Ver logs de la aplicación principal
	@docker compose -f $(COMPOSE_FILE) logs -f main-app

.PHONY: logs-api
logs-api: ## 📝 Ver logs del servicio API
	@docker compose -f $(COMPOSE_FILE) logs -f api-service

.PHONY: shell-traefik
shell-traefik: ## 🐚 Acceder al shell de Traefik
	@docker compose -f $(COMPOSE_FILE) exec traefik sh

.PHONY: inspect-network
inspect-network: ## 🔍 Inspeccionar la red proxy
	@echo -e "$(CYAN)🔍 Información de la red $(NETWORK_NAME):$(NC)"
	@docker network inspect $(NETWORK_NAME) || echo -e "$(RED)❌ Red $(NETWORK_NAME) no encontrada$(NC)"

##@ 🔒 Gestión de Certificados

.PHONY: certs
certs: ## 🔒 Generar nuevos certificados SSL
	@echo -e "$(YELLOW)🔒 Generando certificados SSL...$(NC)"
	@./_scripts/generate-certs.sh
	@echo -e "$(GREEN)✅ Certificados generados$(NC)"

.PHONY: certs-info
certs-info: ## 📋 Ver información de los certificados
	@echo -e "$(CYAN)📋 Información del certificado:$(NC)"
	@if [ -f certs/$(CERT_DOMAIN).crt ]; then \
		openssl x509 -in certs/$(CERT_DOMAIN).crt -text -noout | grep -A1 "Subject:\|Not Before\|Not After\|DNS:"; \
	else \
		echo -e "$(RED)❌ Certificado no encontrado. Ejecuta 'make certs'$(NC)"; \
	fi

.PHONY: certs-clean
certs-clean: ## 🧹 Limpiar certificados generados
	@echo -e "$(YELLOW)🧹 Limpiando certificados...$(NC)"
	@rm -rf certs/*.crt certs/*.key certs/*.conf
	@echo -e "$(GREEN)✅ Certificados eliminados$(NC)"

##@ 🔧 Servicios Individuales

# .PHONY: restart-traefik
# restart-traefik: ## 🔄 Reiniciar solo Traefik
# 	@echo -e "$(YELLOW)🔄 Reiniciando Traefik...$(NC)"
# 	@docker compose -f $(COMPOSE_FILE) restart traefik
# 	@echo -e "$(GREEN)✅ Traefik reiniciado$(NC)"

# .PHONY: restart-app
# restart-app: ## 🔄 Reiniciar aplicación principal
# 	@docker compose -f $(COMPOSE_FILE) restart main-app

# .PHONY: restart-api
# restart-api: ## 🔄 Reiniciar servicio API
# 	@docker compose -f $(COMPOSE_FILE) restart api-service

# .PHONY: scale-api
# scale-api: ## ⚖️  Escalar API a 3 instancias
# 	@echo -e "$(BLUE)⚖️  Escalando API a 3 instancias...$(NC)"
# 	@docker compose -f $(COMPOSE_FILE) up -d --scale api-service=3
# 	@echo -e "$(GREEN)✅ API escalada$(NC)"

# .PHONY: scale-api-down
# scale-api-down: ## ⚖️  Reducir API a 1 instancia
# 	@echo -e "$(BLUE)⚖️  Reduciendo API a 1 instancia...$(NC)"
# 	@docker compose -f $(COMPOSE_FILE) up -d --scale api-service=1
# 	@echo -e "$(GREEN)✅ API reducida$(NC)"

##@ 🧪 Testing y Desarrollo

.PHONY: test
test: ## 🧪 Ejecutar tests de conectividad
	@echo -e "$(CYAN)🧪 Ejecutando tests de conectividad...$(NC)"
	@echo -e "$(BLUE)Testing main app...$(NC)"
	@curl -k -s -o /dev/null -w "Status: %{http_code} - Time: %{time_total}s\n" https://localhost/ || echo "❌ Failed"
	@echo -e "$(BLUE)Testing API...$(NC)"
	@curl -k -s -o /dev/null -w "Status: %{http_code} - Time: %{time_total}s\n" https://localhost/api || echo "❌ Failed"
	@echo -e "$(BLUE)Testing Traefik dashboard...$(NC)"
	@curl -s -o /dev/null -w "Status: %{http_code} - Time: %{time_total}s\n" http://localhost:8080/api/rawdata || echo "❌ Failed"

.PHONY: test-ssl
test-ssl: ## 🔒 Test SSL para todos los dominios
	@echo -e "$(CYAN)🔒 Testing SSL para todos los dominios...$(NC)"
	@for domain in msarknet.me grafana.msarknet.me prom.msarknet.me traefik.msarknet.me portainer.msarknet.me docs.msarknet.me; do \
		echo -e "$(BLUE)Testing $$domain...$(NC)"; \
		curl -k -s -o /dev/null -w "$$domain: %{http_code} (%{time_total}s)\n" https://$$domain/ || echo "❌ $$domain failed"; \
	done

# .PHONY: benchmark
# benchmark: ## 📊 Benchmark básico de rendimiento
# 	@echo -e "$(CYAN)📊 Ejecutando benchmark...$(NC)"
# 	@echo -e "$(BLUE)Benchmark main app (100 requests)...$(NC)"
# 	@ab -n 100 -c 10 -k -H "Host: msarknet.me" https://localhost/ 2>/dev/null | grep -E "Requests per second|Time per request" || echo "❌ ab (Apache Bench) no disponible"

##@ 🧹 Limpieza

.PHONY: clean
clean: ## 🧹 Limpiar contenedores parados y recursos no usados
	@echo -e "$(YELLOW)🧹 Limpiando recursos no usados...$(NC)"
	@docker system prune -f
	@echo -e "$(GREEN)✅ Limpieza completada$(NC)"

.PHONY: clean-all
clean-all: down ## 🧹 Limpieza completa (contenedores, imágenes, volúmenes)
	@echo -e "$(RED)🌐 Eliminando red Docker $(NETWORK_NAME)...$(NC)"
	@docker network rm $(NETWORK_NAME) 2>/dev/null || echo -e "$(YELLOW)⚠️  Red $(NETWORK_NAME) no existe$(NC)"
	@echo -e "$(RED)🧹 Limpieza completa del sistema...$(NC)"
	@docker system prune -a -f --volumes
	@echo -e "$(GREEN)✅ Limpieza completa terminada$(NC)"

.PHONY: reset
reset: down certs-clean ## 🔄 Reset completo del entorno
	@echo -e "$(RED)🔄 Reset completo del entorno...$(NC)"
	@docker system prune -f
	@rm -rf letsencrypt/*
	@echo -e "$(GREEN)✅ Reset completado. Ejecuta 'make install' para reinstalar$(NC)"

##@ 🔧 Configuración

.PHONY: hosts-check
hosts-check: ## 🔍 Verificar configuración de /etc/hosts
	@echo -e "$(CYAN)🔍 Verificando /etc/hosts...$(NC)"
	@echo -e "$(BLUE)Entradas encontradas:$(NC)"
	@grep "msarknet.me" /etc/hosts || echo -e "$(RED)❌ No se encontraron entradas para msarknet.me$(NC)"

.PHONY: hosts-add
hosts-add: ## ➕ Añadir entradas faltantes a /etc/hosts
	@echo -e "$(YELLOW)➕ Añadiendo entradas a /etc/hosts...$(NC)"
	@sudo bash -c 'cat >> /etc/hosts << EOF\n127.0.0.1 msarknet.me\n127.0.0.1 grafana.msarknet.me\n127.0.0.1 prom.msarknet.me\n127.0.0.1 traefik.msarknet.me\n127.0.0.1 portainer.msarknet.me\n127.0.0.1 docs.msarknet.me\nEOF'
	@echo -e "$(GREEN)✅ Entradas añadidas$(NC)"

.PHONY: htpasswd
htpasswd: ## (uso: make htpasswd user=admin pass=admin)
	@if [ -z "$(user)" ] || [ -z "$(pass)" ]; then \
		echo "❌ Debes indicar user y pass, por ejemplo:"; \
		echo "   make htpasswd user=admin pass=admin"; \
		exit 1; \
	fi
	@htpasswd -nbB $(user) "$(pass)"

# .PHONY: network-create
# network-create: ## 🌐 Crear red Docker proxy
# 	@echo -e "$(BLUE)🌐 Creando red Docker $(NETWORK_NAME)...$(NC)"
# 	@docker network create $(NETWORK_NAME) 2>/dev/null || echo -e "$(YELLOW)⚠️  Red $(NETWORK_NAME) ya existe$(NC)"

# .PHONY: network-remove
# network-remove: ## 🌐 Eliminar red Docker proxy
# 	@echo -e "$(RED)🌐 Eliminando red Docker $(NETWORK_NAME)...$(NC)"
# 	@docker network rm $(NETWORK_NAME) 2>/dev/null || echo -e "$(YELLOW)⚠️  Red $(NETWORK_NAME) no existe$(NC)"

##@ 📚 Información y Ayuda

# .PHONY: info
# info: ## ℹ️  Mostrar información del entorno
# 	@echo -e "$(CYAN)ℹ️  Información del entorno MSarkNet:$(NC)"
# 	@echo -e "  $(BLUE)Proyecto:$(NC) $(PROJECT_NAME)"
# 	@echo -e "  $(BLUE)Red Docker:$(NC) $(NETWORK_NAME)"
# 	@echo -e "  $(BLUE)Dominio principal:$(NC) $(CERT_DOMAIN)"
# 	@echo -e "  $(BLUE)Archivo compose:$(NC) $(COMPOSE_FILE)"
# 	@echo ""
# 	@echo -e "$(CYAN)📁 Estructura de archivos:$(NC)"
# 	@ls -la | grep -E "(docker-compose|Makefile|certs|dynamic|web-content)"

# .PHONY: urls
# urls: ## 🌐 Mostrar todas las URLs disponibles
# 	@echo -e "$(CYAN)🌐 URLs disponibles:$(NC)"
# 	@echo -e "  $(GREEN)🏠 Principal:$(NC)      https://msarknet.me"
# 	@echo -e "  $(GREEN)⚙️  Traefik:$(NC)       https://traefik.msarknet.me"
# 	@echo -e "  $(GREEN)📊 Grafana:$(NC)       https://grafana.msarknet.me"
# 	@echo -e "  $(GREEN)📈 Prometheus:$(NC)    https://prom.msarknet.me"
# 	@echo -e "  $(GREEN)🐳 Portainer:$(NC)     https://portainer.msarknet.me"
# 	@echo -e "  $(GREEN)📚 Docs:$(NC)          https://docs.msarknet.me"
# 	@echo -e "  $(GREEN)🔧 API:$(NC)           https://msarknet.me/api"

.PHONY: help
help: ## 💡 Mostrar esta ayuda
	@awk 'BEGIN {FS = ":.*##"; printf "\n$(CYAN)🚀 MSarkNet Traefik Management$(NC)\n\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2 } /^##@/ { printf "\n$(YELLOW)%s$(NC)\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

##@ 🎯 Atajos Rápidos

# .PHONY: dev
# dev: up logs-traefik ## 🚀 Modo desarrollo (up + logs de Traefik)

# .PHONY: quick-test
# quick-test: ## ⚡ Test rápido de funcionamiento
# 	@curl -k -s https://localhost/ | grep -q "MSarkNet" && echo -e "$(GREEN)✅ Test OK$(NC)" || echo -e "$(RED)❌ Test Failed$(NC)"
