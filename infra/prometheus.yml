global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus se monitorea a sí mismo
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Traefik metrics (si decides habilitar métricas)
  - job_name: 'traefik'
    static_configs:
      - targets: ['traefik:8082']
    metrics_path: /metrics
    scrape_interval: 5s

  # Grafana metrics
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    metrics_path: /metrics

  # Node exporter (si lo instalas más tarde)
  # - job_name: 'node'
  #   static_configs:
  #     - targets: ['node-exporter:9100']

  # Whoami service (ejemplo)
  - job_name: 'whoami'
    static_configs:
      - targets: ['whoami:80']
    scrape_interval: 30s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093